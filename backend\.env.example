# Database Configuration
MONGODB_URI=mongodb://localhost:27017/smartswap

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:8080

# Logging
LOG_LEVEL=info

# Redis Configuration (for automated scheduling)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Scheduler Configuration
SCHEDULER_ENABLED=true
SCHEDULER_TIMEZONE=America/New_York
